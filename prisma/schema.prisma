// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  // output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  SUPER_ADMIN
  ADMIN
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BLOCK
}

enum ProjectType {
  Frontend
  Backend
  Fullstack
}

model User {
  id         Int        @id @default(autoincrement())
  name       String
  email      String     @unique
  password   String?
  role       Role       @default(USER)
  phone      String
  picture    String?
  status     UserStatus @default(ACTIVE)
  isVerified Boolean    @default(false)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  blogs      Blog[]
}

model Category {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  blogs     Blog[]
}

model Blog {
  id          Int      @id @default(autoincrement())
  title       String
  slug        String   @unique
  description String
  content     String
  image       String?
  isFeatured  Boolean  @default(false)
  categoryId  Int
  category    Category @relation(fields: [categoryId], references: [id])
  tags        String[]
  views       Int      @default(0)
  authorId    Int
  author      User     @relation(fields: [authorId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Project {
  id           Int         @id @default(autoincrement())
  title        String
  slug         String      @unique
  description  String
  content      String
  image        String?
  type         ProjectType
  projectUrl   String
  codeUrl      String?
  technologies String[]
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
}
